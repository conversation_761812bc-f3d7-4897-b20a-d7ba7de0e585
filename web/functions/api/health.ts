// Health check endpoint for Tinder<PERSON> Helper
// Used by Chrome extension to verify API connectivity

interface HealthResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  service: string;
  version: string;
  timestamp: string;
  checks: {
    api: boolean;
    ai: boolean;
    environment: boolean;
  };
}

// CORS headers for cross-origin requests from extension
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

// Handle preflight requests
export const onRequestOptions: PagesFunction = async () => {
  return new Response(null, {
    status: 200,
    headers: corsHeaders,
  });
};

// Health check GET endpoint
export const onRequestGet: PagesFunction<Env> = async (context) => {
  try {
    console.log('🏥 Health check requested');
    
    // Perform health checks
    const checks = await performHealthChecks(context.env);
    
    // Determine overall status
    const allHealthy = Object.values(checks).every(check => check === true);
    const anyUnhealthy = Object.values(checks).some(check => check === false);
    
    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (allHealthy) {
      status = 'healthy';
    } else if (anyUnhealthy) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }
    
    const response: HealthResponse = {
      status,
      service: 'TinderOP Helper API',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      checks
    };
    
    const httpStatus = status === 'healthy' ? 200 : status === 'degraded' ? 206 : 503;
    
    console.log(`✅ Health check completed: ${status}`);
    
    return new Response(JSON.stringify(response, null, 2), {
      status: httpStatus,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders,
      },
    });
    
  } catch (error) {
    console.error('❌ Health check failed:', error);
    
    const errorResponse: HealthResponse = {
      status: 'unhealthy',
      service: 'TinderOP Helper API',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      checks: {
        api: false,
        ai: false,
        environment: false
      }
    };
    
    return new Response(JSON.stringify(errorResponse, null, 2), {
      status: 503,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders,
      },
    });
  }
};

// Perform individual health checks
async function performHealthChecks(env: any): Promise<{
  api: boolean;
  ai: boolean;
  environment: boolean;
}> {
  const checks = {
    api: true, // API is responsive if we're here
    ai: false,
    environment: false
  };
  
  try {
    // Check environment variables
    checks.environment = !!(env.VITE_OPENROUTER_API_KEY);
    
    // Check AI service connectivity (basic test)
    if (checks.environment) {
      try {
        // We could do a simple test call to OpenRouter here
        // For now, just verify the API key exists and looks valid
        const apiKey = env.VITE_OPENROUTER_API_KEY;
        checks.ai = !!(apiKey && apiKey.length > 10);
      } catch (error) {
        console.warn('⚠️ AI service check failed:', error);
        checks.ai = false;
      }
    }
    
  } catch (error) {
    console.error('❌ Health checks failed:', error);
  }
  
  return checks;
}