// Advanced prompting system for bio analysis with o3

import { getExpertPersona, type ExpertPersona } from '../types/expert-personas';

export interface BioPromptTemplate {
  systemPrompt: string;
  userPrompt: string;
  chainOfThoughtStructure: string;
  examples?: string[];
}

export class AdvancedBioPromptGenerator {
  
  /**
   * Generate expert-specific prompt for bio analysis
   */
  generateExpertPrompt(expertType: string, bio: string, analysisContext?: any): BioPromptTemplate {
    const persona = getExpertPersona(expertType);
    
    const systemPrompt = this.buildSystemPrompt(persona);
    const userPrompt = this.buildUserPrompt(persona, bio, analysisContext);
    const chainOfThoughtStructure = this.buildChainOfThoughtStructure(persona);
    
    return {
      systemPrompt,
      userPrompt,
      chainOfThoughtStructure,
      examples: this.getExamples(expertType)
    };
  }

  /**
   * Build comprehensive system prompt with expert persona
   */
  private buildSystemPrompt(persona: ExpertPersona): string {
    return `You are ${persona.name}, ${persona.credentials}.

BACKGROUND & EXPERTISE:
${persona.background}

Your core expertise includes:
${persona.expertise.map(item => `• ${item}`).join('\n')}

ANALYSIS APPROACH:
${persona.analysisApproach}

ADVANCED REASONING INSTRUCTIONS:
You possess exceptional analytical capabilities. For this bio analysis, you must:

1. LINGUISTIC ANALYSIS: Examine word choice, structure, and communication effectiveness
2. PSYCHOLOGICAL PROFILING: Identify personality traits, emotional intelligence, and authenticity markers
3. MARKET POSITIONING: Assess competitive positioning and target audience alignment
4. CONVERSION OPTIMIZATION: Evaluate potential for matches and meaningful conversations
5. STRATEGIC RECOMMENDATIONS: Provide specific, high-impact improvement suggestions

RESPONSE REQUIREMENTS:
- Use systematic reasoning throughout your analysis
- Support every assessment with specific textual evidence
- Provide confidence levels with clear justification
- Rank recommendations by potential impact
- Balance honesty with constructive feedback

Your analysis will be synthesized with other expert perspectives for comprehensive feedback.`;
  }

  /**
   * Build user prompt with chain-of-thought structure
   */
  private buildUserPrompt(persona: ExpertPersona, bio: string, context?: any): string {
    const contextInfo = context ? this.buildContextInfo(context) : '';
    
    return `${contextInfo}

Please analyze this dating profile bio using your expertise as a ${persona.type} expert.

BIO TO ANALYZE:
"${bio}"

SYSTEMATIC ANALYSIS FRAMEWORK:

1. INITIAL ASSESSMENT PHASE:
   - What is my immediate impression of this bio?
   - What key elements stand out from my expert perspective?
   - What does this bio communicate about the person?

2. DETAILED EXPERT EVALUATION:
   - How does this bio perform in my area of expertise?
   - What specific strengths and weaknesses do I identify?
   - How does this compare to successful bios I've analyzed?

3. PSYCHOLOGICAL/LINGUISTIC/MARKET ANALYSIS:
   - What personality traits or characteristics are evident?
   - How effective is the communication style?
   - What market positioning does this create?

4. SCORING METHODOLOGY:
   - What score (0-100) does this bio deserve in my expert area?
   - What specific evidence supports this assessment?
   - Which factors most influence this score?

5. CONFIDENCE EVALUATION:
   - How confident am I in this analysis (0-100)?
   - What factors support or limit my confidence?
   - What additional context would enhance my assessment?

6. STRATEGIC RECOMMENDATIONS:
   - What are my top 3-5 specific improvement recommendations?
   - Which changes would have the highest impact?
   - What is the implementation difficulty for each suggestion?

RESPONSE FORMAT:
Provide your analysis in this exact JSON structure:

{
  "initial_assessment": {
    "immediate_impression": "Your first expert impression",
    "key_elements": ["element1", "element2", "element3"],
    "overall_communication": "What this bio communicates about the person"
  },
  "expert_evaluation": {
    "strengths": ["strength1", "strength2"],
    "weaknesses": ["weakness1", "weakness2"],
    "expert_specific_analysis": "Analysis from your specific expertise",
    "comparative_assessment": "How this compares to successful bios"
  },
  "specialized_analysis": {
    "personality_indicators": ["trait1", "trait2"],
    "communication_effectiveness": "Assessment of communication style",
    "market_positioning": "How this positions the person in the dating market",
    "target_audience_appeal": "Who this would appeal to"
  },
  "scoring_methodology": {
    "score": 75,
    "evidence": ["evidence1", "evidence2", "evidence3"],
    "key_factors": ["factor1", "factor2"],
    "score_components": {
      "component1": 80,
      "component2": 70,
      "component3": 75
    }
  },
  "confidence_evaluation": {
    "confidence": 88,
    "supporting_factors": ["factor1", "factor2"],
    "limiting_factors": ["limitation1", "limitation2"],
    "context_needed": "What additional context would help"
  },
  "strategic_recommendations": [
    {
      "recommendation": "Specific actionable improvement",
      "impact_level": "high|medium|low",
      "implementation_difficulty": "easy|moderate|challenging",
      "reasoning": "Why this recommendation is important",
      "priority": 1,
      "expected_outcome": "What improvement this would create"
    }
  ],
  "key_insights": ["insight1", "insight2", "insight3"]
}

Apply your specialized expertise while maintaining a constructive and helpful tone.`;
  }

  /**
   * Build chain-of-thought structure for bio analysis
   */
  private buildChainOfThoughtStructure(persona: ExpertPersona): string {
    return `
CHAIN-OF-THOUGHT REASONING STRUCTURE FOR BIO ANALYSIS:

Step 1: Expert First Impression
- "As a ${persona.type} expert, my immediate reaction to this bio is..."
- "The key elements that catch my attention are..."
- "From my professional perspective, this bio suggests..."

Step 2: Specialized Analysis Application
- "Applying my expertise in ${persona.expertise[0]}, I observe..."
- "The linguistic/psychological/market factors indicate..."
- "Based on my experience with ${persona.specializations[0]}, this demonstrates..."

Step 3: Comparative Market Assessment
- "Compared to successful bios I've analyzed..."
- "This bio would rank in the [percentile] because..."
- "The competitive positioning appears to be..."

Step 4: Evidence-Based Scoring
- "I'm scoring this [X]/100 in my expert area because..."
- "The specific evidence supporting this score includes..."
- "The primary factors influencing this assessment are..."

Step 5: Strategic Improvement Planning
- "The highest impact improvement would be..."
- "Based on my expertise, the priority recommendations are..."
- "The expected outcomes of these changes would be..."
`;
  }

  /**
   * Build context information for bio analysis
   */
  private buildContextInfo(context: any): string {
    let contextStr = 'ANALYSIS CONTEXT:\n';
    
    if (context.targetDemographic) {
      contextStr += `• Target Demographic: ${context.targetDemographic}\n`;
    }
    
    if (context.platform) {
      contextStr += `• Platform: ${context.platform}\n`;
    }
    
    if (context.relationshipGoals) {
      contextStr += `• Relationship Goals: ${context.relationshipGoals}\n`;
    }
    
    if (context.tone) {
      contextStr += `• Desired Tone: ${context.tone}\n`;
    }
    
    return contextStr + '\n';
  }

  /**
   * Get few-shot examples for the expert type
   */
  private getExamples(expertType: string): string[] {
    const examples: Record<string, string[]> = {
      psychology: [
        `Example Bio: "Love hiking, good food, and deep conversations. Looking for someone who can make me laugh and isn't afraid to be vulnerable."
        Analysis: "This bio demonstrates emotional intelligence through the mention of 'deep conversations' and 'vulnerability,' suggesting secure attachment style. The balance of activities and emotional needs indicates psychological maturity. Score: 82/100 because it signals healthy relationship readiness."`,
        
        `Example Bio: "Just looking for fun, nothing serious. Hit me up if you're down for whatever."
        Analysis: "The language suggests avoidant attachment patterns and potential commitment issues. The vague 'whatever' indicates poor boundary setting. Score: 34/100 because this signals emotional unavailability and may attract incompatible matches."`
      ],
      
      dating_coach: [
        `Example Bio: "Entrepreneur who loves weekend adventures and trying new restaurants. Seeking a partner in crime for life's next chapter. Dog lover and terrible cook - you've been warned!"
        Analysis: "Excellent balance of accomplishment, lifestyle, and humor. The 'partner in crime' suggests relationship readiness while maintaining independence. Self-deprecating humor shows confidence. Score: 89/100 - this would generate quality matches and conversations."`,
        
        `Example Bio: "I'm perfect, looking for my soulmate. Must be 6'+ and love fitness. No drama or games."
        Analysis: "The tone is demanding and creates barriers rather than attraction. 'Perfect' suggests narcissistic tendencies, while the requirements list appears shallow. Score: 28/100 because this would repel quality matches."`
      ],
      
      data_science: [
        `Example Bio: "Software engineer who rock climbs on weekends. Love craft beer and board games. Looking for someone to explore the city with."
        Analysis: "This bio type performs in the 78th percentile for professional males 25-35. The hobby diversity and activity-based connection points correlate with higher match rates. Score: 81/100 based on engagement analytics."`,
        
        `Example Bio: "Just ask me anything you want to know."
        Analysis: "This bio style shows 23% lower engagement rates and 45% fewer quality conversations. Lack of conversation starters reduces algorithmic visibility. Score: 31/100 based on platform performance data."`
      ],
      
      fashion: [
        `Example Bio: "Vintage vinyl collector with a weakness for Sunday farmers markets. Equally comfortable in hiking boots or dress shoes. Seeking someone who appreciates both adventure and elegance."
        Analysis: "The style references suggest sophisticated taste and versatility. The contrast between 'hiking boots or dress shoes' demonstrates range and adaptability. Score: 85/100 because this communicates strong personal style and lifestyle flexibility."`,
        
        `Example Bio: "Love partying and getting wasted every weekend. Looking for someone who can keep up with my lifestyle."
        Analysis: "This lifestyle presentation lacks sophistication and may signal poor judgment. The focus on excessive drinking creates negative style associations. Score: 22/100 because this projects an unappealing lifestyle brand."`
      ]
    };
    
    return examples[expertType] || [];
  }
}
