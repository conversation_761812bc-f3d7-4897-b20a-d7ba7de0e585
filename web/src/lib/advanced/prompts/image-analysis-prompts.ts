// Advanced prompting system for image analysis with o3

import { getExpertPersona, type ExpertPersona } from '../types/expert-personas';

export interface PromptTemplate {
  systemPrompt: string;
  userPrompt: string;
  chainOfThoughtStructure: string;
  examples?: string[];
}

export class AdvancedImagePromptGenerator {
  
  /**
   * Generate expert-specific prompt for image analysis
   */
  generateExpertPrompt(expertType: string, analysisContext?: any): PromptTemplate {
    const persona = getExpertPersona(expertType);
    
    const systemPrompt = this.buildSystemPrompt(persona);
    const userPrompt = this.buildUserPrompt(persona, analysisContext);
    const chainOfThoughtStructure = this.buildChainOfThoughtStructure(persona);
    
    return {
      systemPrompt,
      userPrompt,
      chainOfThoughtStructure,
      examples: this.getExamples(expertType)
    };
  }

  /**
   * Build comprehensive system prompt with expert persona
   */
  private buildSystemPrompt(persona: ExpertPersona): string {
    return `You are ${persona.name}, ${persona.credentials}.

BACKGROUND & EXPERTISE:
${persona.background}

Your core expertise includes:
${persona.expertise.map(item => `• ${item}`).join('\n')}

ANALYSIS APPROACH:
${persona.analysisApproach}

ADVANCED REASONING INSTRUCTIONS:
You are equipped with exceptional reasoning capabilities. For this analysis, you must:

1. SYSTEMATIC OBSERVATION: Carefully catalog all relevant visual elements
2. EXPERT ANALYSIS: Apply your specialized knowledge and experience
3. EVIDENCE-BASED REASONING: Support every assessment with specific observations
4. COMPARATIVE CONTEXT: Consider how this compares to successful dating profiles
5. ACTIONABLE INSIGHTS: Provide specific, implementable recommendations

RESPONSE REQUIREMENTS:
- Use chain-of-thought reasoning throughout your analysis
- Provide evidence for every score and assessment
- Include confidence levels with justification
- Prioritize recommendations by impact potential
- Maintain professional expertise while being constructive

Your analysis will be combined with other experts to provide comprehensive feedback.`;
  }

  /**
   * Build user prompt with chain-of-thought structure
   */
  private buildUserPrompt(persona: ExpertPersona, context?: any): string {
    const contextInfo = context ? this.buildContextInfo(context) : '';
    
    return `${contextInfo}

Please analyze this dating profile image using your expertise as a ${persona.type} expert.

ANALYSIS FRAMEWORK:
Follow this systematic approach:

1. INITIAL OBSERVATION PHASE:
   - What are the key visual elements I observe?
   - What stands out immediately from my expert perspective?
   - What technical/aesthetic/psychological factors are present?

2. DETAILED EXPERT ANALYSIS:
   - What specific improvements are needed from my expert perspective?
   - What concrete changes would optimize this for dating success?
   - How can this be enhanced to follow best practices in my field?
   - Focus on ACTIONABLE IMPROVEMENTS, not just observations

3. SCORING RATIONALE:
   - Based on my analysis, what score (0-100) does this deserve?
   - What specific evidence supports this score?
   - What factors contribute most to this assessment?

4. CONFIDENCE ASSESSMENT:
   - How confident am I in this analysis (0-100)?
   - What factors increase or decrease my confidence?
   - What additional information would improve my assessment?

5. ACTIONABLE RECOMMENDATIONS:
   - What are the top 3-5 most impactful improvements needed?
   - Which changes would have the highest impact on dating success?
   - What is the effort level required for each improvement?
   - Be specific about HOW to implement each improvement

RESPONSE FORMAT:
Provide your analysis in this exact JSON structure:

{
  "observation_phase": {
    "key_elements": ["element1", "element2", "element3"],
    "immediate_impressions": "Your first impressions as an expert",
    "technical_factors": ["factor1", "factor2"]
  },
  "expert_evaluation": {
    "expert_specific_analysis": "Focus on SPECIFIC IMPROVEMENTS needed, not just observations. What exactly should be changed, improved, or optimized? Provide actionable insights.",
    "strengths": ["strength1", "strength2"],
    "weaknesses": ["weakness1", "weakness2"]
  },
  "scoring_methodology": {
    "score": 85,
    "evidence": ["evidence1", "evidence2", "evidence3"],
    "key_factors": ["factor1", "factor2"]
  },
  "confidence_evaluation": {
    "confidence": 92,
    "supporting_factors": ["factor1", "factor2"],
    "limiting_factors": ["area1", "area2"]
  },
  "strategic_recommendations": [
    {
      "recommendation": "Specific actionable improvement advice",
      "impact_level": "high",
      "implementation_difficulty": "easy",
      "reasoning": "Why this improvement matters and how it helps"
    },
    {
      "recommendation": "Another specific improvement",
      "impact_level": "medium",
      "implementation_difficulty": "moderate",
      "reasoning": "Detailed reasoning for this improvement"
    }
  ]
}

Remember: Your analysis should reflect your specific expertise while being constructive and actionable.`;
  }

  /**
   * Build chain-of-thought structure for the expert
   */
  private buildChainOfThoughtStructure(persona: ExpertPersona): string {
    return `
CHAIN-OF-THOUGHT REASONING STRUCTURE:

Step 1: Initial Expert Observation
- "As a ${persona.type} expert, I immediately notice..."
- "The key elements that stand out to me are..."
- "From my professional perspective, this image shows..."

Step 2: Technical/Professional Analysis
- "Applying my expertise in ${persona.expertise[0]}, I can see..."
- "The technical quality/psychological factors/style elements indicate..."
- "Based on my experience with ${persona.specializations[0]}, this demonstrates..."

Step 3: Comparative Assessment
- "Compared to successful profiles I've analyzed..."
- "This ranks in the [percentile] of images I've evaluated because..."
- "The market positioning would be..."

Step 4: Evidence-Based Scoring
- "I'm scoring this [X]/100 because..."
- "The evidence supporting this score includes..."
- "The main factors influencing this score are..."

Step 5: Strategic Recommendations
- "The highest impact improvement would be..."
- "Based on my expertise, I recommend..."
- "The priority order for improvements should be..."
`;
  }

  /**
   * Build context information for the analysis
   */
  private buildContextInfo(context: any): string {
    let contextStr = 'ANALYSIS CONTEXT:\n';
    
    if (context.targetDemographic) {
      contextStr += `• Target Demographic: ${context.targetDemographic}\n`;
    }
    
    if (context.platform) {
      contextStr += `• Platform: ${context.platform}\n`;
    }
    
    if (context.analysisDepth) {
      contextStr += `• Analysis Depth: ${context.analysisDepth}\n`;
    }
    
    return contextStr + '\n';
  }

  /**
   * Get few-shot examples for the expert type
   */
  private getExamples(expertType: string): string[] {
    const examples: Record<string, string[]> = {
      photography: [
        `Example: "As a photography expert, I immediately notice the harsh overhead lighting creating unflattering shadows under the eyes and nose. The composition is off-center without artistic intent, and the background is cluttered with distracting elements. Score: 45/100 because while the image is technically sharp, the lighting and composition significantly detract from the subject's appeal."`,
        
        `Example: "This image demonstrates excellent use of natural window light, creating soft, even illumination that enhances facial features. The rule of thirds is well-applied, and the shallow depth of field creates pleasing background blur. Score: 88/100 - this is professional-quality portrait work that would perform well on dating platforms."`
      ],
      
      psychology: [
        `Example: "From a psychological perspective, the subject displays genuine Duchenne smile markers - crow's feet and raised cheeks - indicating authentic happiness. The direct eye contact suggests confidence and openness. Body language appears relaxed and approachable. Score: 82/100 because these psychological cues strongly signal attractiveness and trustworthiness."`,
        
        `Example: "The facial expression appears forced, with tension visible in the jaw and a smile that doesn't reach the eyes. The slightly averted gaze may signal insecurity or discomfort. Score: 58/100 because these psychological markers could reduce perceived authenticity and approachability."`
      ],
      
      fashion: [
        `Example: "The styling demonstrates excellent understanding of color theory - the navy shirt complements the subject's skin tone beautifully. The fit is impeccable, showing attention to tailoring. The casual-professional aesthetic is perfect for dating profiles. Score: 91/100 - this is exemplary personal styling."`,
        
        `Example: "The outfit choice is problematic - the oversized graphic tee appears juvenile and doesn't flatter the body shape. The colors clash with the subject's complexion. Score: 42/100 because the styling choices significantly detract from the subject's potential appeal."`
      ],
      
      data_science: [
        `Example: "Based on platform analytics, this image type (outdoor, genuine smile, good lighting) typically performs in the 85th percentile for engagement. The composition and subject presentation align with high-performing profile characteristics. Score: 87/100 based on predictive modeling data."`,
        
        `Example: "This image style (poor lighting, forced expression, cluttered background) correlates with below-average performance metrics. Platform algorithms typically deprioritize such images. Score: 38/100 based on conversion rate analysis."`
      ],
      
      dating_coach: [
        `Example: "This image perfectly captures approachability and confidence - key factors for dating success. The genuine expression and relaxed posture invite conversation. The setting suggests an interesting lifestyle. Score: 89/100 because this image would generate high match rates and quality conversations."`,
        
        `Example: "While the subject is attractive, the image lacks personality and conversation starters. The generic setting and neutral expression don't provide talking points. Score: 61/100 because it may generate matches but struggle with engagement."`
      ]
    };
    
    return examples[expertType] || [];
  }
}
