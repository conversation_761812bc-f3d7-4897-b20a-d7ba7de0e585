// TinderOP Helper - Content Script
// Runs in the context of dating app pages to provide overlay UI

class TinderOPHelper {
  constructor() {
    this.isInitialized = false;
    this.isAnalyzing = false;
    this.overlayContainer = null;
    this.fab = null;
    this.suggestionPanel = null;
    
    // Configuration
    this.config = {
      apiUrl: 'http://localhost:5173', // Development URL - will be configurable
      retryAttempts: 3,
      retryDelay: 1000
    };
    
    console.log('🚀 TinderOP Helper initialized');
    this.init();
  }

  async init() {
    if (this.isInitialized) return;
    
    try {
      // Wait for page to be fully loaded
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => this.setupUI());
      } else {
        this.setupUI();
      }
      
      this.isInitialized = true;
      console.log('✅ TinderOP Helper setup complete');
    } catch (error) {
      console.error('❌ Failed to initialize TinderOP Helper:', error);
    }
  }

  setupUI() {
    // Create overlay container
    this.createOverlayContainer();
    
    // Create floating action button
    this.createFloatingActionButton();
    
    // Create suggestion panel (hidden initially)
    this.createSuggestionPanel();
    
    // Set up event listeners
    this.setupEventListeners();
  }

  createOverlayContainer() {
    // Remove existing overlay if present
    const existing = document.getElementById('tinderop-helper-overlay');
    if (existing) existing.remove();
    
    this.overlayContainer = document.createElement('div');
    this.overlayContainer.id = 'tinderop-helper-overlay';
    this.overlayContainer.className = 'tinderop-overlay';
    
    // Append to body
    document.body.appendChild(this.overlayContainer);
    
    console.log('📱 Overlay container created');
  }

  createFloatingActionButton() {
    this.fab = document.createElement('button');
    this.fab.id = 'tinderop-fab';
    this.fab.className = 'tinderop-fab';
    this.fab.innerHTML = `
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
        <path d="M8 9h8M8 13h6"></path>
      </svg>
    `;
    this.fab.title = 'Get conversation suggestions';
    
    this.overlayContainer.appendChild(this.fab);
    console.log('🎯 Floating action button created');
  }

  createSuggestionPanel() {
    this.suggestionPanel = document.createElement('div');
    this.suggestionPanel.id = 'tinderop-suggestions';
    this.suggestionPanel.className = 'tinderop-suggestions hidden';
    
    this.suggestionPanel.innerHTML = `
      <div class="suggestions-header">
        <h3>💬 Conversation Suggestions</h3>
        <button class="close-btn" id="tinderop-close">×</button>
      </div>
      <div class="suggestions-content">
        <div class="loading-state" id="tinderop-loading">
          <div class="spinner"></div>
          <p>Analyzing conversation...</p>
        </div>
        <div class="suggestions-list" id="tinderop-suggestions-list"></div>
      </div>
    `;
    
    this.overlayContainer.appendChild(this.suggestionPanel);
    console.log('💭 Suggestion panel created');
  }

  setupEventListeners() {
    // FAB click handler
    this.fab.addEventListener('click', () => this.handleFabClick());
    
    // Close button handler
    const closeBtn = document.getElementById('tinderop-close');
    closeBtn.addEventListener('click', () => this.hideSuggestionPanel());
    
    // Click outside to close
    document.addEventListener('click', (e) => {
      if (!this.overlayContainer.contains(e.target)) {
        this.hideSuggestionPanel();
      }
    });
    
    // Escape key to close
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.hideSuggestionPanel();
      }
    });
    
    console.log('🎮 Event listeners set up');
  }

  async handleFabClick() {
    if (this.isAnalyzing) return;
    
    try {
      this.isAnalyzing = true;
      this.showSuggestionPanel();
      this.showLoadingState();
      
      // Request screenshot from background script
      const screenshot = await this.captureScreenshot();
      
      // Extract conversation context from page
      const context = this.extractConversationContext();
      
      // Send to API for analysis
      const suggestions = await this.analyzeCon versation(screenshot, context);
      
      // Display suggestions
      this.displaySuggestions(suggestions);
      
    } catch (error) {
      console.error('❌ Failed to analyze conversation:', error);
      this.showError('Failed to analyze conversation. Please try again.');
    } finally {
      this.isAnalyzing = false;
      this.hideLoadingState();
    }
  }

  async captureScreenshot() {
    return new Promise((resolve, reject) => {
      // Send message to background script to capture screenshot
      chrome.runtime.sendMessage(
        { action: 'captureScreenshot' },
        (response) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else if (response.error) {
            reject(new Error(response.error));
          } else {
            resolve(response.screenshot);
          }
        }
      );
    });
  }

  extractConversationContext() {
    // This will be platform-specific logic to extract conversation context
    // For now, return basic information
    const context = {
      platform: this.detectPlatform(),
      url: window.location.href,
      timestamp: Date.now(),
      messages: this.extractVisibleMessages()
    };
    
    console.log('📝 Extracted context:', context);
    return context;
  }

  detectPlatform() {
    const hostname = window.location.hostname;
    if (hostname.includes('tinder.com')) return 'tinder';
    if (hostname.includes('bumble.com')) return 'bumble';
    if (hostname.includes('hinge.co')) return 'hinge';
    return 'unknown';
  }

  extractVisibleMessages() {
    // Platform-specific message extraction
    // This is a basic implementation - will need refinement for each platform
    const messages = [];
    
    // Common selectors that might work across platforms
    const messageSelectors = [
      '[data-testid*="message"]',
      '.message',
      '.chat-message',
      '[class*="message"]'
    ];
    
    for (const selector of messageSelectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        elements.forEach(el => {
          const text = el.textContent.trim();
          if (text && text.length > 0) {
            messages.push(text);
          }
        });
        break; // Use first working selector
      }
    }
    
    return messages.slice(-10); // Return last 10 messages
  }

  async analyzeConversation(screenshot, context) {
    const payload = {
      screenshot,
      context,
      timestamp: Date.now()
    };
    
    const response = await fetch(`${this.config.apiUrl}/api/tinder-helper/analyze`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload)
    });
    
    if (!response.ok) {
      throw new Error(`API request failed: ${response.status}`);
    }
    
    const result = await response.json();
    return result.suggestions || [];
  }

  showSuggestionPanel() {
    this.suggestionPanel.classList.remove('hidden');
    this.fab.classList.add('active');
  }

  hideSuggestionPanel() {
    this.suggestionPanel.classList.add('hidden');
    this.fab.classList.remove('active');
  }

  showLoadingState() {
    document.getElementById('tinderop-loading').style.display = 'block';
    document.getElementById('tinderop-suggestions-list').style.display = 'none';
  }

  hideLoadingState() {
    document.getElementById('tinderop-loading').style.display = 'none';
    document.getElementById('tinderop-suggestions-list').style.display = 'block';
  }

  displaySuggestions(suggestions) {
    const container = document.getElementById('tinderop-suggestions-list');
    
    if (!suggestions || suggestions.length === 0) {
      container.innerHTML = '<p class="no-suggestions">No suggestions available. Try again!</p>';
      return;
    }
    
    container.innerHTML = suggestions.map((suggestion, index) => `
      <div class="suggestion-item" data-index="${index}">
        <div class="suggestion-text">${this.escapeHtml(suggestion.text)}</div>
        <div class="suggestion-meta">
          <span class="tone">${suggestion.tone || 'neutral'}</span>
          <button class="copy-btn" data-text="${this.escapeHtml(suggestion.text)}">Copy</button>
        </div>
      </div>
    `).join('');
    
    // Add copy button event listeners
    container.querySelectorAll('.copy-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const text = e.target.getAttribute('data-text');
        this.copyToClipboard(text);
        e.target.textContent = 'Copied!';
        setTimeout(() => {
          e.target.textContent = 'Copy';
        }, 2000);
      });
    });
  }

  showError(message) {
    const container = document.getElementById('tinderop-suggestions-list');
    container.innerHTML = `<p class="error-message">❌ ${this.escapeHtml(message)}</p>`;
  }

  async copyToClipboard(text) {
    try {
      await navigator.clipboard.writeText(text);
      console.log('📋 Text copied to clipboard');
    } catch (error) {
      console.error('Failed to copy text:', error);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
    }
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new TinderOPHelper();
  });
} else {
  new TinderOPHelper();
}